{"Version": 1, "Hash": "fpZlpQouNu4N/BJsqhBh3Cu5AI1CRKfp0Tkx5keOarQ=", "Source": "ThemisSupportTools.Web", "BasePath": "_content/ThemisSupportTools.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor.csproj", "Version": 2, "Source": "Core.Themis.Libraries.Razor", "GetPublishAssetsTargets": "ComputeReferencedStaticWebAssetsPublishManifest;GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained"}], "DiscoveryPatterns": [{"Name": "Core.Themis.Libraries.Razor\\wwwroot", "Source": "Core.Themis.Libraries.Razor", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "Pattern": "**"}, {"Name": "ThemisSupportTools.Web\\wwwroot", "Source": "ThemisSupportTools.Web", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\", "BasePath": "_content/ThemisSupportTools.Web", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "7t9tbfaemk", "Integrity": "FYJNevdgdMX4R7rEhQxDvKVdV5SyZSlRT4eVSU+Lgyk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.ai.js", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "blazor.bootstrap.ai.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "m4v3m943t1", "Integrity": "qqVhOIiDDbSiSP44XiXacBrWpys+pgsREG/S9PlabcQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.ai.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.css", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "blazor.bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "63oqyoiiv4", "Integrity": "jqE3jyGxWnCTc0Y7oGfHKZOpZM9udgb38MQGLaxmkNc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.js", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "blazor.bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mgojsf1q78", "Integrity": "ajVMnqBMAkGl+pstlYFA4TwJBU1G7yqG5K5q51SUDJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.pdf.js", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "blazor.bootstrap.pdf.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ct0ej5e0q0", "Integrity": "oAIj2P0Z/6hTwjGA3OGvWLdxMP4KwE35+AQzWQTpxAg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.pdf.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.sortable-list.js", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "blazor.bootstrap.sortable-list.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ezlxc6gzv3", "Integrity": "du71NM6e2+m9l81ldV+Gbr74ER7Xx1GnJFg+rIY6OQU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.sortable-list.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.theme-switcher.js", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "blazor.bootstrap.theme-switcher.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pisakkcwob", "Integrity": "Hy2y6B5ohWPnmWg4c52+QUuZGTw8r09KtWKcaGiaa/k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.theme-switcher.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\icon\\128X128.png", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "icon/128X128.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tngynhsog2", "Integrity": "J1yB7ftYf6PWiU1781eE0JHBGfd1MV/XlLaxpVy09RU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\icon\\128X128.png"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.min.js", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "pdfjs-4.0.379.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j8zp7bt7w3", "Integrity": "XX7we2CiSWzj3rsNtKFCod90LBYAJ3bo8rmu4EuN7SQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.min.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.worker.min.js", "SourceId": "Blazor.Bootstrap", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "BasePath": "_content/Blazor.Bootstrap", "RelativePath": "pdfjs-4.0.379.worker.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "79hai7knhw", "Integrity": "JbwqIn9Fic7JuWyTXuNnAA1T0dYQ2tkTi9HhrSltUtQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.worker.min.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\BlazorDateRangePicker.bundle.scp.css", "SourceId": "BlazorDateRangePicker", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\", "BasePath": "_content/BlazorDateRangePicker", "RelativePath": "BlazorDateRangePicker.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "iitr8bszy4", "Integrity": "1p2vug7PF4nK8g3cCr3/Gge2gk18Zm5z3uBJvJgYmTc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\BlazorDateRangePicker.bundle.scp.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\clickAndPositionHandler.js", "SourceId": "BlazorDateRangePicker", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\", "BasePath": "_content/BlazorDateRangePicker", "RelativePath": "clickAndPositionHandler.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lywn4it7vj", "Integrity": "Y13FGi0LwdilZTDiSJhwO9kI/dHRax0/H52S+Yq1Ae0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\clickAndPositionHandler.js"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\ThemisSupportTools.Web.styles.css", "SourceId": "ThemisSupportTools.Web", "SourceType": "Computed", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/ThemisSupportTools.Web", "RelativePath": "ThemisSupportTools.Web#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "ylfhbds5ty", "Integrity": "RqiSNm3lFegKOV/O0KAqBmiG7uz2KipUZGdA6JhV+Fs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\ThemisSupportTools.Web.styles.css"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\ThemisSupportTools.Web.bundle.scp.css", "SourceId": "ThemisSupportTools.Web", "SourceType": "Computed", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/ThemisSupportTools.Web", "RelativePath": "ThemisSupportTools.Web#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "k3y8vupvmb", "Integrity": "YJprrmK0nGzkvIud9d824WCyNr83s0WzGwmRyxkIlsE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\ThemisSupportTools.Web.bundle.scp.css"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\app.css", "SourceId": "ThemisSupportTools.Web", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\", "BasePath": "_content/ThemisSupportTools.Web", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "etl2an9hxi", "Integrity": "Dk/mcCakf36iLXbLAKyXVaitZcgT8jPq1RZmJju7Iyg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\bootstrap\\bootstrap.min.css", "SourceId": "ThemisSupportTools.Web", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\", "BasePath": "_content/ThemisSupportTools.Web", "RelativePath": "bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\bootstrap\\bootstrap.min.css.map", "SourceId": "ThemisSupportTools.Web", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\", "BasePath": "_content/ThemisSupportTools.Web", "RelativePath": "bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css.map"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\favicon.png", "SourceId": "ThemisSupportTools.Web", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\", "BasePath": "_content/ThemisSupportTools.Web", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\js\\partners-select2.js", "SourceId": "ThemisSupportTools.Web", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\", "BasePath": "_content/ThemisSupportTools.Web", "RelativePath": "js/partners-select2#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vrqqezomlt", "Integrity": "C0RqvpeAhPwNIkUn9ttKoXa4dFjlavj5rVC1cDznAbk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\partners-select2.js"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\js\\script.js", "SourceId": "ThemisSupportTools.Web", "SourceType": "Discovered", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\", "BasePath": "_content/ThemisSupportTools.Web", "RelativePath": "js/script#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j8l5lslnt7", "Integrity": "PdAK+p4HpIzKISnEhvV7OvOAK2o5QNOua19JJuuCiak=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\script.js"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Inputs\\InputImageFileCustom.razor.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "Common/Components/Inputs/InputImageFileCustom.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rdyabvu3r3", "Integrity": "ccweldhAso2sfXeANbzb5jYQI7O/NkuK9Tlq4h3t2Po=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Inputs\\InputImageFileCustom.razor.js"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Inputs\\InputTextAreaCustom.razor.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "Common/Components/Inputs/InputTextAreaCustom.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d5ekrygsx9", "Integrity": "oUcB2LItpvcgkYGkN97ecjOwkITwL/lzyNOSNCcBLg0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Inputs\\InputTextAreaCustom.razor.js"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\NewSelect2.razor.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "Common/Components/Select/NewSelect2.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7w0wqjb5ml", "Integrity": "eb2deh7PoNWpHQ4+YMICF+YEaIDO3gmLmqXWycT0KKI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\NewSelect2.razor.js"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\Select2.razor.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "Common/Components/Select/Select2.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xa30f2o21h", "Integrity": "ucewIiDK4fR08DxCMwwUebyXCP5pu3oJ7qrxd4D/52k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\Select2.razor.js"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Sortable\\SortableList.razor.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "Common/Components/Sortable/SortableList.razor.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "p4z3zfclgx", "Integrity": "Z4yIrMx7kzjpXXSToBSVfjAiXKUAGqlz+QQyUp/E7dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Sortable\\SortableList.razor.js"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Core.Themis.Libraries.Razor.bundle.scp.css", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "Core.Themis.Libraries.Razor#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "rpcwcmxhht", "Integrity": "oQPDZULlPD/UennMlHROUfEZpUfIj/BRlI1sMA3Cv5E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Core.Themis.Libraries.Razor.bundle.scp.css"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\common.less", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "css/common#[.{fingerprint}]?.less", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fslu2o6p56", "Integrity": "zwuEp4gcF5ZD9pAMEnToX2fLvGxQrETvwOlBY/wIK1o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\common.less"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\widget.css", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "css/widget#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "42ua3cf6ss", "Integrity": "rLl3dmthmc98ZULjpxd9cvCdoyw+EoVu0ewC3yX5yr4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\widget.css"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\common_razor_library.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "js/common_razor_library#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sdkbw9k9a5", "Integrity": "EGBoi0rztui08chZbWM/W/RLe29Zbuqi0aTSPNUE5vw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\common_razor_library.js"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\phoneInput.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "js/phoneInput#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5yj6xghg17", "Integrity": "g0Ri1GCVlpamze46nPLzhd2dvyhYg8UIb//BtJN/JBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\phoneInput.js"}, {"Identity": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\widget.js", "SourceId": "Core.Themis.Libraries.Razor", "SourceType": "Project", "ContentRoot": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\", "BasePath": "_content/Core.Themis.Libraries.Razor", "RelativePath": "js/widget#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nufmjs66fb", "Integrity": "QUMUr76nISq50oC+k8kFz37FvrVp/cSW94yMx6sq+RI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\widget.js"}], "Endpoints": [{"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.63oqyoiiv4.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22639"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jqE3jyGxWnCTc0Y7oGfHKZOpZM9udgb38MQGLaxmkNc=\""}, {"Name": "Last-Modified", "Value": "Sat, 09 Nov 2024 14:43:36 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "63oqyoiiv4"}, {"Name": "integrity", "Value": "sha256-jqE3jyGxWnCTc0Y7oGfHKZOpZM9udgb38MQGLaxmkNc="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/blazor.bootstrap.css"}]}, {"Route": "_content/Blazor.Bootstrap/Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14166"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"FYJNevdgdMX4R7rEhQxDvKVdV5SyZSlRT4eVSU+Lgyk=\""}, {"Name": "Last-Modified", "Value": "Sat, 02 Nov 2024 09:45:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7t9tbfaemk"}, {"Name": "integrity", "Value": "sha256-FYJNevdgdMX4R7rEhQxDvKVdV5SyZSlRT4eVSU+Lgyk="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/Blazor.Bootstrap.bundle.scp.css"}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.ai.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.ai.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2000"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qqVhOIiDDbSiSP44XiXacBrWpys+pgsREG/S9PlabcQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 07:52:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qqVhOIiDDbSiSP44XiXacBrWpys+pgsREG/S9PlabcQ="}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.ai.m4v3m943t1.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.ai.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2000"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qqVhOIiDDbSiSP44XiXacBrWpys+pgsREG/S9PlabcQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 07:52:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m4v3m943t1"}, {"Name": "integrity", "Value": "sha256-qqVhOIiDDbSiSP44XiXacBrWpys+pgsREG/S9PlabcQ="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/blazor.bootstrap.ai.js"}]}, {"Route": "_content/Blazor.Bootstrap/Blazor.Bootstrap.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14166"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"FYJNevdgdMX4R7rEhQxDvKVdV5SyZSlRT4eVSU+Lgyk=\""}, {"Name": "Last-Modified", "Value": "Sat, 02 Nov 2024 09:45:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FYJNevdgdMX4R7rEhQxDvKVdV5SyZSlRT4eVSU+Lgyk="}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22639"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jqE3jyGxWnCTc0Y7oGfHKZOpZM9udgb38MQGLaxmkNc=\""}, {"Name": "Last-Modified", "Value": "Sat, 09 Nov 2024 14:43:36 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jqE3jyGxWnCTc0Y7oGfHKZOpZM9udgb38MQGLaxmkNc="}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "79921"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ajVMnqBMAkGl+pstlYFA4TwJBU1G7yqG5K5q51SUDJU=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 07:52:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ajVMnqBMAkGl+pstlYFA4TwJBU1G7yqG5K5q51SUDJU="}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.mgojsf1q78.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "79921"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ajVMnqBMAkGl+pstlYFA4TwJBU1G7yqG5K5q51SUDJU=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Sep 2024 07:52:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mgojsf1q78"}, {"Name": "integrity", "Value": "sha256-ajVMnqBMAkGl+pstlYFA4TwJBU1G7yqG5K5q51SUDJU="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/blazor.bootstrap.js"}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.pdf.ct0ej5e0q0.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.pdf.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7290"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oAIj2P0Z/6hTwjGA3OGvWLdxMP4KwE35+AQzWQTpxAg=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Jul 2024 07:36:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ct0ej5e0q0"}, {"Name": "integrity", "Value": "sha256-oAIj2P0Z/6hTwjGA3OGvWLdxMP4KwE35+AQzWQTpxAg="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/blazor.bootstrap.pdf.js"}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.pdf.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.pdf.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7290"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oAIj2P0Z/6hTwjGA3OGvWLdxMP4KwE35+AQzWQTpxAg=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Jul 2024 07:36:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oAIj2P0Z/6hTwjGA3OGvWLdxMP4KwE35+AQzWQTpxAg="}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.sortable-list.ezlxc6gzv3.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.sortable-list.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1930"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"du71NM6e2+m9l81ldV+Gbr74ER7Xx1GnJFg+rIY6OQU=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Apr 2024 19:30:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ezlxc6gzv3"}, {"Name": "integrity", "Value": "sha256-du71NM6e2+m9l81ldV+Gbr74ER7Xx1GnJFg+rIY6OQU="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/blazor.bootstrap.sortable-list.js"}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.sortable-list.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.sortable-list.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1930"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"du71NM6e2+m9l81ldV+Gbr74ER7Xx1GnJFg+rIY6OQU=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Apr 2024 19:30:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-du71NM6e2+m9l81ldV+Gbr74ER7Xx1GnJFg+rIY6OQU="}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.theme-switcher.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.theme-switcher.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2896"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Hy2y6B5ohWPnmWg4c52+QUuZGTw8r09KtWKcaGiaa/k=\""}, {"Name": "Last-Modified", "Value": "Wed, 20 Nov 2024 15:30:37 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hy2y6B5ohWPnmWg4c52+QUuZGTw8r09KtWKcaGiaa/k="}]}, {"Route": "_content/Blazor.Bootstrap/blazor.bootstrap.theme-switcher.pisakkcwob.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\blazor.bootstrap.theme-switcher.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2896"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Hy2y6B5ohWPnmWg4c52+QUuZGTw8r09KtWKcaGiaa/k=\""}, {"Name": "Last-Modified", "Value": "Wed, 20 Nov 2024 15:30:37 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pisakkcwob"}, {"Name": "integrity", "Value": "sha256-Hy2y6B5ohWPnmWg4c52+QUuZGTw8r09KtWKcaGiaa/k="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/blazor.bootstrap.theme-switcher.js"}]}, {"Route": "_content/Blazor.Bootstrap/icon/128X128.png", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\icon\\128X128.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "7074"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"J1yB7ftYf6PWiU1781eE0JHBGfd1MV/XlLaxpVy09RU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 19 Apr 2022 11:45:43 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J1yB7ftYf6PWiU1781eE0JHBGfd1MV/XlLaxpVy09RU="}]}, {"Route": "_content/Blazor.Bootstrap/icon/128X128.tngynhsog2.png", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\icon\\128X128.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7074"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"J1yB7ftYf6PWiU1781eE0JHBGfd1MV/XlLaxpVy09RU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 19 Apr 2022 11:45:43 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tngynhsog2"}, {"Name": "integrity", "Value": "sha256-J1yB7ftYf6PWiU1781eE0JHBGfd1MV/XlLaxpVy09RU="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/icon/128X128.png"}]}, {"Route": "_content/Blazor.Bootstrap/pdfjs-4.0.379.min.j8zp7bt7w3.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "305685"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XX7we2CiSWzj3rsNtKFCod90LBYAJ3bo8rmu4EuN7SQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Jul 2024 07:36:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j8zp7bt7w3"}, {"Name": "integrity", "Value": "sha256-XX7we2CiSWzj3rsNtKFCod90LBYAJ3bo8rmu4EuN7SQ="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/pdfjs-4.0.379.min.js"}]}, {"Route": "_content/Blazor.Bootstrap/pdfjs-4.0.379.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "305685"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XX7we2CiSWzj3rsNtKFCod90LBYAJ3bo8rmu4EuN7SQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Jul 2024 07:36:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XX7we2CiSWzj3rsNtKFCod90LBYAJ3bo8rmu4EuN7SQ="}]}, {"Route": "_content/Blazor.Bootstrap/pdfjs-4.0.379.worker.min.79hai7knhw.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.worker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1029159"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JbwqIn9Fic7JuWyTXuNnAA1T0dYQ2tkTi9HhrSltUtQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Jul 2024 07:36:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "79hai7knhw"}, {"Name": "integrity", "Value": "sha256-JbwqIn9Fic7JuWyTXuNnAA1T0dYQ2tkTi9HhrSltUtQ="}, {"Name": "label", "Value": "_content/Blazor.Bootstrap/pdfjs-4.0.379.worker.min.js"}]}, {"Route": "_content/Blazor.Bootstrap/pdfjs-4.0.379.worker.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\pdfjs-4.0.379.worker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1029159"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JbwqIn9Fic7JuWyTXuNnAA1T0dYQ2tkTi9HhrSltUtQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 15 Jul 2024 07:36:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JbwqIn9Fic7JuWyTXuNnAA1T0dYQ2tkTi9HhrSltUtQ="}]}, {"Route": "_content/BlazorDateRangePicker/BlazorDateRangePicker.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\BlazorDateRangePicker.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10857"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"1p2vug7PF4nK8g3cCr3/Gge2gk18Zm5z3uBJvJgYmTc=\""}, {"Name": "Last-Modified", "Value": "Mon, 22 Jan 2024 15:56:12 GMT"}], "EndpointProperties": []}, {"Route": "_content/BlazorDateRangePicker/clickAndPositionHandler.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\clickAndPositionHandler.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5344"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Y13FGi0LwdilZTDiSJhwO9kI/dHRax0/H52S+Yq1Ae0=\""}, {"Name": "Last-Modified", "Value": "Mon, 22 Jan 2024 17:14:55 GMT"}], "EndpointProperties": []}, {"Route": "_content/Core.Themis.Libraries.Razor/Common/Components/Inputs/InputImageFileCustom.razor.js", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Inputs\\InputImageFileCustom.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "122"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ccweldhAso2sfXeANbzb5jYQI7O/NkuK9Tlq4h3t2Po=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 08:30:35 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ccweldhAso2sfXeANbzb5jYQI7O/NkuK9Tlq4h3t2Po="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/Common/Components/Inputs/InputTextAreaCustom.razor.js", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Inputs\\InputTextAreaCustom.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "200"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oUcB2LItpvcgkYGkN97ecjOwkITwL/lzyNOSNCcBLg0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 08:30:35 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oUcB2LItpvcgkYGkN97ecjOwkITwL/lzyNOSNCcBLg0="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/Common/Components/Select/NewSelect2.razor.js", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\NewSelect2.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1833"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eb2deh7PoNWpHQ4+YMICF+YEaIDO3gmLmqXWycT0KKI=\""}, {"Name": "Last-Modified", "Value": "Fri, 04 Jul 2025 08:55:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eb2deh7PoNWpHQ4+YMICF+YEaIDO3gmLmqXWycT0KKI="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/Common/Components/Select/Select2.razor.js", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\Select2.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1850"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ucewIiDK4fR08DxCMwwUebyXCP5pu3oJ7qrxd4D/52k=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 08:30:35 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ucewIiDK4fR08DxCMwwUebyXCP5pu3oJ7qrxd4D/52k="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/Common/Components/Sortable/SortableList.razor.js", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Sortable\\SortableList.razor.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Z4yIrMx7kzjpXXSToBSVfjAiXKUAGqlz+QQyUp/E7dg=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Dec 2024 13:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Z4yIrMx7kzjpXXSToBSVfjAiXKUAGqlz+QQyUp/E7dg="}, {"Name": "script-type", "Value": "module"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor.bundle.scp.css", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Core.Themis.Libraries.Razor.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "804"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oQPDZULlPD/UennMlHROUfEZpUfIj/BRlI1sMA3Cv5E=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 08 Jul 2025 09:40:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oQPDZULlPD/UennMlHROUfEZpUfIj/BRlI1sMA3Cv5E="}]}, {"Route": "_content/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor.rpcwcmxhht.bundle.scp.css", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\Core.Themis.Libraries.Razor.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "804"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oQPDZULlPD/UennMlHROUfEZpUfIj/BRlI1sMA3Cv5E=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 08 Jul 2025 09:40:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rpcwcmxhht"}, {"Name": "integrity", "Value": "sha256-oQPDZULlPD/UennMlHROUfEZpUfIj/BRlI1sMA3Cv5E="}, {"Name": "label", "Value": "_content/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor.bundle.scp.css"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/css/common.fslu2o6p56.less", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\common.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "759"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"zwuEp4gcF5ZD9pAMEnToX2fLvGxQrETvwOlBY/wIK1o=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 08:30:35 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fslu2o6p56"}, {"Name": "integrity", "Value": "sha256-zwuEp4gcF5ZD9pAMEnToX2fLvGxQrETvwOlBY/wIK1o="}, {"Name": "label", "Value": "_content/Core.Themis.Libraries.Razor/css/common.less"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/css/common.less", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\common.less", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "759"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"zwuEp4gcF5ZD9pAMEnToX2fLvGxQrETvwOlBY/wIK1o=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 08:30:35 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zwuEp4gcF5ZD9pAMEnToX2fLvGxQrETvwOlBY/wIK1o="}]}, {"Route": "_content/Core.Themis.Libraries.Razor/css/widget.42ua3cf6ss.css", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\widget.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "26069"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rLl3dmthmc98ZULjpxd9cvCdoyw+EoVu0ewC3yX5yr4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 08:30:35 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "42ua3cf6ss"}, {"Name": "integrity", "Value": "sha256-rLl3dmthmc98ZULjpxd9cvCdoyw+EoVu0ewC3yX5yr4="}, {"Name": "label", "Value": "_content/Core.Themis.Libraries.Razor/css/widget.css"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/css/widget.css", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\css\\widget.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "26069"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rLl3dmthmc98ZULjpxd9cvCdoyw+EoVu0ewC3yX5yr4=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 08:30:35 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rLl3dmthmc98ZULjpxd9cvCdoyw+EoVu0ewC3yX5yr4="}]}, {"Route": "_content/Core.Themis.Libraries.Razor/js/common_razor_library.js", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\common_razor_library.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4059"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EGBoi0rztui08chZbWM/W/RLe29Zbuqi0aTSPNUE5vw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 08:30:35 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EGBoi0rztui08chZbWM/W/RLe29Zbuqi0aTSPNUE5vw="}]}, {"Route": "_content/Core.Themis.Libraries.Razor/js/common_razor_library.sdkbw9k9a5.js", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\common_razor_library.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4059"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EGBoi0rztui08chZbWM/W/RLe29Zbuqi0aTSPNUE5vw=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 08:30:35 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sdkbw9k9a5"}, {"Name": "integrity", "Value": "sha256-EGBoi0rztui08chZbWM/W/RLe29Zbuqi0aTSPNUE5vw="}, {"Name": "label", "Value": "_content/Core.Themis.Libraries.Razor/js/common_razor_library.js"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/js/phoneInput.5yj6xghg17.js", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\phoneInput.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3820"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g0Ri1GCVlpamze46nPLzhd2dvyhYg8UIb//BtJN/JBQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 04 Jul 2025 08:55:40 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5yj6xghg17"}, {"Name": "integrity", "Value": "sha256-g0Ri1GCVlpamze46nPLzhd2dvyhYg8UIb//BtJN/JBQ="}, {"Name": "label", "Value": "_content/Core.Themis.Libraries.Razor/js/phoneInput.js"}]}, {"Route": "_content/Core.Themis.Libraries.Razor/js/phoneInput.js", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\phoneInput.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3820"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"g0Ri1GCVlpamze46nPLzhd2dvyhYg8UIb//BtJN/JBQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 04 Jul 2025 08:55:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g0Ri1GCVlpamze46nPLzhd2dvyhYg8UIb//BtJN/JBQ="}]}, {"Route": "_content/Core.Themis.Libraries.Razor/js/widget.js", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\widget.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17940"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QUMUr76nISq50oC+k8kFz37FvrVp/cSW94yMx6sq+RI=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Dec 2024 13:51:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QUMUr76nISq50oC+k8kFz37FvrVp/cSW94yMx6sq+RI="}]}, {"Route": "_content/Core.Themis.Libraries.Razor/js/widget.nufmjs66fb.js", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\js\\widget.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17940"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QUMUr76nISq50oC+k8kFz37FvrVp/cSW94yMx6sq+RI=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Dec 2024 13:51:40 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nufmjs66fb"}, {"Name": "integrity", "Value": "sha256-QUMUr76nISq50oC+k8kFz37FvrVp/cSW94yMx6sq+RI="}, {"Name": "label", "Value": "_content/Core.Themis.Libraries.Razor/js/widget.js"}]}, {"Route": "app.css", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Dk/mcCakf36iLXbLAKyXVaitZcgT8jPq1RZmJju7Iyg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 14:55:07 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Dk/mcCakf36iLXbLAKyXVaitZcgT8jPq1RZmJju7Iyg="}]}, {"Route": "app.etl2an9hxi.css", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33102"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Dk/mcCakf36iLXbLAKyXVaitZcgT8jPq1RZmJju7Iyg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 14:55:07 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "etl2an9hxi"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-Dk/mcCakf36iLXbLAKyXVaitZcgT8jPq1RZmJju7Iyg="}]}, {"Route": "bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Dec 2024 13:51:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Dec 2024 13:51:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Dec 2024 13:51:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Dec 2024 13:51:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Dec 2024 13:51:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "label", "Value": "favicon.png"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "favicon.png", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Dec 2024 13:51:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "js/partners-select2.js", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\js\\partners-select2.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2797"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"C0RqvpeAhPwNIkUn9ttKoXa4dFjlavj5rVC1cDznAbk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 12 Aug 2025 07:11:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C0RqvpeAhPwNIkUn9ttKoXa4dFjlavj5rVC1cDznAbk="}]}, {"Route": "js/partners-select2.vrqqezomlt.js", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\js\\partners-select2.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2797"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"C0RqvpeAhPwNIkUn9ttKoXa4dFjlavj5rVC1cDznAbk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 12 Aug 2025 07:11:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vrqqezomlt"}, {"Name": "label", "Value": "js/partners-select2.js"}, {"Name": "integrity", "Value": "sha256-C0RqvpeAhPwNIkUn9ttKoXa4dFjlavj5rVC1cDznAbk="}]}, {"Route": "js/script.j8l5lslnt7.js", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\js\\script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8359"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PdAK+p4HpIzKISnEhvV7OvOAK2o5QNOua19JJuuCiak=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 15:58:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j8l5lslnt7"}, {"Name": "label", "Value": "js/script.js"}, {"Name": "integrity", "Value": "sha256-PdAK+p4HpIzKISnEhvV7OvOAK2o5QNOua19JJuuCiak="}]}, {"Route": "js/script.js", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\js\\script.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8359"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PdAK+p4HpIzKISnEhvV7OvOAK2o5QNOua19JJuuCiak=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 15:58:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PdAK+p4HpIzKISnEhvV7OvOAK2o5QNOua19JJuuCiak="}]}, {"Route": "ThemisSupportTools.Web.bundle.scp.css", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\ThemisSupportTools.Web.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5927"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YJprrmK0nGzkvIud9d824WCyNr83s0WzGwmRyxkIlsE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 09:44:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YJprrmK0nGzkvIud9d824WCyNr83s0WzGwmRyxkIlsE="}]}, {"Route": "ThemisSupportTools.Web.k3y8vupvmb.bundle.scp.css", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\ThemisSupportTools.Web.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5927"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YJprrmK0nGzkvIud9d824WCyNr83s0WzGwmRyxkIlsE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 09:44:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k3y8vupvmb"}, {"Name": "label", "Value": "ThemisSupportTools.Web.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-YJprrmK0nGzkvIud9d824WCyNr83s0WzGwmRyxkIlsE="}]}, {"Route": "ThemisSupportTools.Web.styles.css", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\ThemisSupportTools.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6193"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RqiSNm3lFegKOV/O0KAqBmiG7uz2KipUZGdA6JhV+Fs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 09:44:26 GMT"}, {"Name": "Link", "Value": "<_content/Blazor.Bootstrap/Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css>; rel=\"preload\"; as=\"style\", <_content/BlazorDateRangePicker/BlazorDateRangePicker.bundle.scp.css>; rel=\"preload\"; as=\"style\", <_content/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor.rpcwcmxhht.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RqiSNm3lFegKOV/O0KAqBmiG7uz2KipUZGdA6JhV+Fs="}]}, {"Route": "ThemisSupportTools.Web.ylfhbds5ty.styles.css", "AssetFile": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\ThemisSupportTools.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6193"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"RqiSNm3lFegKOV/O0KAqBmiG7uz2KipUZGdA6JhV+Fs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 09:44:26 GMT"}, {"Name": "Link", "Value": "<_content/Blazor.Bootstrap/Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css>; rel=\"preload\"; as=\"style\", <_content/BlazorDateRangePicker/BlazorDateRangePicker.bundle.scp.css>; rel=\"preload\"; as=\"style\", <_content/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor.rpcwcmxhht.bundle.scp.css>; rel=\"preload\"; as=\"style\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ylfhbds5ty"}, {"Name": "integrity", "Value": "sha256-RqiSNm3lFegKOV/O0KAqBmiG7uz2KipUZGdA6JhV+Fs="}, {"Name": "label", "Value": "ThemisSupportTools.Web.styles.css"}]}]}