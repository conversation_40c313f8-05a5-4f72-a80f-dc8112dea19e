﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Managers.TST.Interfaces;
using Core.Themis.Libraries.BLL.Services.Access.Models;
using Core.Themis.Libraries.Data.DBContext.Interfaces;
using Core.Themis.Libraries.Data.Entities.CouponsPromo;
using Core.Themis.Libraries.Data.Entities.WsAdmin.ThemisSupportTools;
using Core.Themis.Libraries.Data.Repositories.CouponsPromo;
using Core.Themis.Libraries.Data.Repositories.CouponsPromo.Interfaces;
using Core.Themis.Libraries.Data.Repositories.LieuPhysique.Interface;
using Core.Themis.Libraries.Data.Repositories.Open.BuyerProfil.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Identities.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.InfoComp.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Language.Intrefaces;
using Core.Themis.Libraries.Data.Repositories.Open.ParamsDeflag;
using Core.Themis.Libraries.Data.Repositories.Open.ParamsDeflag.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Structure.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Tarif.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Transactionnals.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WSAdmin.ThemisSupportTools.Interfaces;
using Core.Themis.Libraries.DTO.CouponsPromo;
using Core.Themis.Libraries.DTO.LieuPhysique;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.ProfiteurAcheteur;
using Core.Themis.Libraries.DTO.ThemisSupportTools.ConfigIniModels;
using Core.Themis.Libraries.DTO.TST;
using Core.Themis.Libraries.Utilities.Extensions;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authorization.Infrastructure;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Org.BouncyCastle.Asn1.Cms;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using System.Xml.Linq;

namespace Core.Themis.Libraries.BLL.Managers.TST;

public class ThemisSupportToolsManager : IThemisSupportToolsManager
{
    private readonly IMapper _mapper;
    private static readonly RodrigueNLogger RodrigueLogger = new RodrigueNLogger();
    private readonly RodrigueNLogger _rodrigueNLogger;

    private readonly IThemisSupportToolsDbContext _themisSupportToolsDbContext;
    private readonly IRepositoryFactory _repositoryFactory;

    private readonly ITstActiveDirectoryGroupRepository _tstActiveDirectoryGroupRepository;
    private readonly IConfiguration _configuration;
    //private readonly ITstModuleRepository _tstModuleRepository;
    //private readonly ITstRoleRepository _tstRoleRepository;
    private readonly ITstAccessRepository _tstAccessRepository;


    private readonly IOfferRepository _offerRepository;
    private readonly IDeviseRepository _deviseRepository;
    private readonly ILanguageRepository _languageRepository;
    private readonly ILibelleTelRepository _libelleTelRepository;
    private readonly IOperatorRepository _operatorRepository;
    private readonly IBuyerProfilRepository _buyerProfilRepository;
    private readonly IInfoCompRepository _infoCompRepository;
    private readonly ITstRoleRepository _tstRoleRepository;
    private readonly ITstModuleRepository _tstModuleRepository;
    private readonly IFiliereRepository _filiereRepository;
    private readonly IPaymentMethodRepository _paymentMethodRepository;
    private readonly IParamsDeflagRepository _paramsDeflagRepository;
    private readonly ICouponsPromoRepository _couponsPromoRepository;
    private readonly ILieuPhysiqueRepository _lieuPhysiqueRepository;

    private const string ValeurParDefaut = "valeurParDefaut";



    private readonly string _tstConfigIniPath;
    private readonly string _customerFilesPath;


    private readonly Dictionary<string, (DateTime CacheTime, List<SelectLookup> Data)> _cache
                                          = new Dictionary<string, (DateTime, List<SelectLookup>)>();


    public ThemisSupportToolsManager(IMapper mapper,
        ITstActiveDirectoryGroupRepository tstActiveDirectoryGroupRepository, ITstAccessRepository tstAccessRepository,
        IConfiguration config, RodrigueNLogger rodrigueNLogger, IDeviseRepository deviseRepository, IOfferRepository offerRepository,
        ILanguageRepository languageRepository, ILibelleTelRepository libelleTelRepository, IOperatorRepository operatorRepository,
        IBuyerProfilRepository buyerProfilRepository, IInfoCompRepository infoCompRepository, IThemisSupportToolsDbContext themisSupportToolsDbContext,
        IRepositoryFactory repositoryFactory,
        ITstRoleRepository tstRoleRepository, ITstModuleRepository tstModuleRepository, IFiliereRepository filiereRepository,
        IPaymentMethodRepository paymentMethodRepository, IParamsDeflagRepository paramsDeflagRepository, ICouponsPromoRepository couponsPromoRepository,
        ILieuPhysiqueRepository lieuPhysiqueRepository)
    {

        _mapper = mapper;
        _tstActiveDirectoryGroupRepository = tstActiveDirectoryGroupRepository;
        //_tstModuleRepository = tstModuleRepository;
        //_tstRoleRepository = tstRoleRepository;
        _tstAccessRepository = tstAccessRepository;

        // _offerRepository = offerRepository;
        _deviseRepository = deviseRepository;
        _languageRepository = languageRepository;
        _libelleTelRepository = libelleTelRepository;
        _operatorRepository = operatorRepository;
        _buyerProfilRepository = buyerProfilRepository;
        _infoCompRepository = infoCompRepository;

        _configuration = config;

        _tstConfigIniPath = config["TSTConfigIniPath"]!;
        _customerFilesPath = config["CustomerFilesPath"]!;
        _rodrigueNLogger = rodrigueNLogger;
        _themisSupportToolsDbContext = themisSupportToolsDbContext;
        _repositoryFactory = repositoryFactory;
        _offerRepository = offerRepository;
        _tstModuleRepository = tstModuleRepository;
        _tstRoleRepository = tstRoleRepository;
        _filiereRepository = filiereRepository;
        _paymentMethodRepository = paymentMethodRepository;
        _paramsDeflagRepository = paramsDeflagRepository;
        _couponsPromoRepository = couponsPromoRepository;
        _lieuPhysiqueRepository = lieuPhysiqueRepository;

    }

    #region Gestion des droits TST

    public async Task<bool> CheckRightsOfUser(ClaimsPrincipal user, AuthorizationPolicy policy)
    {

        var groupSids = user.Claims
           .Where(c => c.Type == "http://schemas.microsoft.com/ws/2008/06/identity/claims/groupsid")
           .Select(c => c.Value)
           .ToList();

        var activeDirectoryGroupEntities = await _tstActiveDirectoryGroupRepository.GetActiveDirectoryGroupsRepositoryWithDependanciesAsync();
        activeDirectoryGroupEntities = activeDirectoryGroupEntities.Where(r => groupSids.Contains(r.ActiveDirectoryGroupExternalId)).ToList();

        //Rôles attribués pour l'utilisateur dans la base de données
        var tstRoles = activeDirectoryGroupEntities.SelectMany(ad => ad.TstRoles).ToList();
        //Liste des rôles que la page demande
        List<string> rolesOfpoliciyRequirement = ExtractRolesOfRequirementPolicy(policy);

        // Comparaison des rôles utilisateur avec ceux de la policy
        var userHasRequirementRoles = tstRoles.Any(tstr => rolesOfpoliciyRequirement.Contains(tstr.RoleName));

        return userHasRequirementRoles;
    }

    public async Task<List<TstActiveDirectoryGroupDTO>?> GetActiveDirectoryGroupWithDependanciesByExternalIdsAsync(List<string> activeDirectoryGroupExternalIds)
    {
        //var result = await _tstActiveDirectoryGroupRepository.GetActiveDirectoryGroupWithDependanciesByExternalIdsAsync(activeDirectoryGroupExternalIds);
        var result = await _tstActiveDirectoryGroupRepository.GetActiveDirectoryGroupsRepositoryWithDependanciesAsync();

        result = result.Where(adg => activeDirectoryGroupExternalIds.Contains(adg.ActiveDirectoryGroupExternalId)).ToList();

        var resultDTO = _mapper.Map<List<TstActiveDirectoryGroupDTO>>(result);

        return resultDTO;
    }

    public TstRoleDTO? GetTstAccessByModuleIdAndActiveDirectoryGroupId(int moduleId, List<int> activeDirectoryGroupIds)
    {
        var access = _tstAccessRepository
            .FindCustomEntityBy(a => a.ModuleId == moduleId)
            .Include(r => r.TstRole)
            .Where(a => activeDirectoryGroupIds.Contains(a.ActiveDirectoryGroupId))
            .ToEntityList();


        var tstAccessByMaxLevel = access.Select(a => a.TstRole).MaxBy(r => r.Level);

        return _mapper.Map<TstRoleDTO>(tstAccessByMaxLevel);

    }

    public List<TstAccessModel> GetTstAccessByActiveDirectoryGroupsId(string[] activeDirectoryGroupIds)
    {
        // Récupère toutes les entités d'accès en incluant les groupes AD, modules et rôles associés
        var access = _tstAccessRepository
            .GetAllCustomEntities()
            .Include(g => g.TstActiveDirectoryGroup)
            .Where(a => activeDirectoryGroupIds.Contains(a.TstActiveDirectoryGroup.ActiveDirectoryGroupExternalId))
            .Include(m => m.TstModule)
            .Include(r => r.TstRole)
            .ToEntityList();

        // Mappe les entités d'accès vers des modèles d'accès
        return _mapper.Map<List<TstAccessModel>>(access);
    }

    public List<string> ExtractRolesOfRequirementPolicy(AuthorizationPolicy policy)
    {
        // Récupère les rôles autorisés depuis la policy
        var policyRoles = new List<string>();

        foreach (var requirement in policy.Requirements)
        {
            if (requirement is RolesAuthorizationRequirement rolesRequirement)
            {
                policyRoles.AddRange(rolesRequirement.AllowedRoles);
            }
        }
        return policyRoles;
    }

    #endregion




    public async Task<TstConfigIniViewModel> GetConfigIniAsync(int structureId, string currentUser)
    {


        if (string.IsNullOrWhiteSpace(currentUser))
        {
            throw new System.ArgumentException($"'{nameof(currentUser)}' ne peut pas avoir une valeur null ou être un espace blanc.",
                nameof(currentUser));
        }

        TstConfigIniViewModel tstConfigIniViewModel = new TstConfigIniViewModel();

        string customerFilePath = GetCustomerPath(structureId);

        string configIniOfUser = ExtractUserNameFromConfigIniUser(customerFilePath);

        bool hasTempFile = HasTempFile(structureId, configIniOfUser);

        if (hasTempFile)
        {
            var TimeLeft = new TimeSpan(0, int.Parse(_configuration["timerInMinute"]!), 0);
            var isExpiredTempfile = IsTempFileOld(structureId, configIniOfUser, TimeLeft);
            if (isExpiredTempfile)
            {
                bool isDeleted = DeleteConfigIniTempFile(structureId, configIniOfUser);
                if (isDeleted)
                    hasTempFile = true;
            }

            if (currentUser != configIniOfUser)
            {
                //erreur
                tstConfigIniViewModel.Error = "Un autre utilisateur a un fichier temporaire en cours d'utilisation";
                tstConfigIniViewModel.UserNameWithTempFile = configIniOfUser;
                return tstConfigIniViewModel;
            }

        }

        await DeleteFileExistsInEnvironmentAsync(currentUser);


        CreateTempFile(structureId, currentUser);


        var configIniSections = GetCustomerFusion(structureId)
                       .OrderBy(section => section.SectionName)
                       .Select(section =>
                       {
                           section.SectionFields = [.. section.SectionFields.OrderBy(field => field.FieldName)];
                           return section;
                       }).ToList();

        /////  var configIniExceptions = GetExceptionsModel(configIniSections);
        //  configIniSections = DeleteExceptionsExternes(configIniSections);

        // EnumEnvironment enumEnvironment = Enum.Parse<EnumEnvironment>(environnement, true);


        // using (var context = _themisSupportToolsDbContext.UseEnvironment(enumEnvironment))
        // {
        //  var offerRepository = _repositoryFactory.CreateRepository<OfferRepository>(DbContextType.ThemisSupportTools);
        SetSelectDatas(configIniSections);
        SetSelectValue(structureId, configIniSections);

        // }

        tstConfigIniViewModel.ConfigIniAllSections = configIniSections;


        tstConfigIniViewModel.ConfigIniSectionsOfUser = configIniSections.Where(s =>
                     s.SectionFields.Any(v => !string.IsNullOrWhiteSpace(v.FieldValue) && s.ChildrenHasValue)).ToList();

        tstConfigIniViewModel.ConfigIniSectionsOfUser.ForEach(s =>
        {
            s.SectionFields.Where(k => !string.IsNullOrEmpty(k.ConnectedToGroup)).ToList().ForEach(f =>
            {
                f.FieldMultipleValue = f.FieldValue.Split(',');
            });
        });

        ExcludeExterneKeys(ref tstConfigIniViewModel);

        tstConfigIniViewModel.ConfigIniAllSections = configIniSections;


        return tstConfigIniViewModel;
    }



    private static void ExcludeExterneKeys(ref TstConfigIniViewModel tstConfigIniViewModel)
    {
        tstConfigIniViewModel.ConfigIniSectionsExcludeOfUser = tstConfigIniViewModel.ConfigIniSectionsOfUser.Where(s => s.SectionEtat.Contains("EXTERNE")).ToList();

        tstConfigIniViewModel.ConfigIniSectionsOfUser.RemoveAll(s => s.SectionEtat.Contains("EXTERNE"));
    }

    private void IncludeExterneKeys(ref TstConfigIniViewModel tstConfigIniViewModel)
    {
        tstConfigIniViewModel.ConfigIniSectionsOfUser.AddRange(tstConfigIniViewModel.ConfigIniSectionsExcludeOfUser);
    }




    public void SetSelectDatas(List<ConfigIniSectionDTO> configIniSections)
    {
        var allGroups = configIniSections.SelectMany(s => s.SectionFields)
                                            .Where(f => !string.IsNullOrWhiteSpace(f.Groupe))
                                            .Select(f => f.Groupe.ToLower()).Distinct().ToList();

        allGroups.ForEach(g =>
        {
            var filedsWithGroupeAttribute = configIniSections.SelectMany(s => s.SectionFields)
                                                 .Where(f => f.Groupe?.ToLower() == g).ToList();

            //Valeurs de tous les selects                
            filedsWithGroupeAttribute.ForEach(f =>
            {
                var valuesToDisabled = filedsWithGroupeAttribute.Where(gf => !string.IsNullOrWhiteSpace(gf.FieldValue) && !gf.FieldValue.Equals(f.FieldValue, StringComparison.CurrentCultureIgnoreCase))
                                                                .Select(gf => gf.FieldValue.ToLower());

                f.SelectData.ForEach(d =>
                {
                    d.IsDisabled = false;
                    if (valuesToDisabled.Contains(d.Value.ToLower()))
                        d.IsDisabled = true;
                });
            });
        });
    }

    public void SetSelectValue(int structureId, List<ConfigIniSectionDTO> configIniSections)
    {
        configIniSections.SelectMany(s => s.SectionFields).ToList()
            .ForEach(f =>
            {
                if (f.TableName is not null)
                {

                    if (f.IsMultiple.HasValue && f.IsMultiple.Value)
                    {
                        f.SelectData = GetMultipleSelectedData(structureId, f);
                        // f.SelectData = GetMultipleSelectedData(structureId, f.TableName, f.TableFieldName, f.TableFieldValue, f.FieldValue);

                        //if (f.SelectData.Any(f => f.IsSelected))
                        //    f.FieldMultipleValue = f.FieldValue.Split(','); 
                        //f.FieldMultipleValue = f.SelectData.Where(f => f.IsSelected).Select(f => f.Value).ToArray();
                    }
                    else
                    {
                        //f.SelectData = GetSelectedData(structureId, f.TableName, f.TableFieldName, f.TableFieldValue, f.FieldValue);
                        f.SelectData = GetSelectedData(structureId, f);
                        //if (f.SelectData.Any(f => f.IsSelected))
                        //    f.FieldValue = f.SelectData.First(f => f.IsSelected).Value;

                    }

                }
                else
                {
                    f.SelectData.ForEach(d =>
                    {
                        if (f.FieldValue is not null && d.Value.ToString() == f.FieldValue.ToString())
                        {
                            d.IsSelected = true;
                        }
                    });
                }

            });

    }


    private List<SelectLookup> GetMultipleSelectedData(int structureId, ConfigIniSectionFieldDTO field)
    {
        List<SelectLookup> result;


        switch (field.TableName)
        {

            case var name when string.Equals(name, "offre", StringComparison.InvariantCultureIgnoreCase):

                // Utiliser le contexte ThemisSupportTools
                // var offerRepository = _repositoryFactory.CreateRepository<OfferRepository>(DbContextType.ThemisSupportTools);

                var offers = _offerRepository.GetAll(structureId);
                if (offers is not null)
                {
                    offers = offers.Where(o => o.DateFinValidite > DateTime.Now);

                    result = offers.Select(x => new SelectLookup
                    {

                        Libelle = x.GetType().GetProperty(field.TableFieldName, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString(),
                        Value = x.GetType().GetProperty(field.TableFieldValue, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString(),

                    }).OrderBy(x => x.Libelle).ToList();

                    if (!string.IsNullOrWhiteSpace(field.FieldValue))
                    {
                        var offresIds = field.FieldValue.Split(',');

                        if (offresIds.Count() > 0)
                        {
                            result.ForEach(v =>
                            {
                                v.IsSelected = offresIds.Contains(v.Value);
                            });
                        }
                    }
                }
                else
                {
                    result = new List<SelectLookup>();
                }
                break;

            case var name when string.Equals(name, "profil_acheteur", StringComparison.InvariantCultureIgnoreCase):

                var profilsAcheteurs = _buyerProfilRepository.GetAll(structureId);
                result = profilsAcheteurs.Select(x => new SelectLookup
                {

                    Libelle = x.GetType().GetProperty(field.TableFieldName, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString(),
                    Value = x.GetType().GetProperty(field.TableFieldValue, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString(),

                }).OrderBy(x => x.Libelle).ToList();

                if (!string.IsNullOrWhiteSpace(field.FieldValue))
                {
                    var profilAcheteurIds = field.FieldValue.Split(',');

                    if (profilAcheteurIds.Count() > 0)
                    {
                        result.ForEach(v =>
                        {
                            v.IsSelected = profilAcheteurIds.Contains(v.Value);
                        });
                    }
                }

                break;

            case var name when string.Equals(name, "info_comp", StringComparison.InvariantCultureIgnoreCase):

                var infoComps = _infoCompRepository.GetAll(structureId);
                result = infoComps.Select(x => new SelectLookup
                {

                    Libelle = x.GetType().GetProperty(field.TableFieldName, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString(),
                    Value = x.GetType().GetProperty(field.TableFieldValue, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString(),

                }).OrderBy(x => x.Libelle).ToList();

                if (!string.IsNullOrWhiteSpace(field.FieldValue))
                {
                    var infoCompIds = field.FieldValue.Split(',');

                    if (infoCompIds.Count() > 0)
                    {
                        result.ForEach(v =>
                        {
                            v.IsSelected = infoCompIds.Contains(v.Value);
                        });
                    }
                }

                break;

            default:
                throw new NotImplementedException();
        }

        return result;
    }
    private List<SelectLookup> GetSelectedData(int structureId, ConfigIniSectionFieldDTO field)
    {
        string cacheKey = $"{structureId}_{field.TableName.ToLower()}_{field.TableFieldName.ToLower()}_{field.TableFieldValue.ToLower()}_{field.FieldName.ToLower()}_{field.FieldValue.ToLower()}";
        List<SelectLookup> result;

        if (_cache.TryGetValue(cacheKey, out var cacheEntry))
        {
            if ((DateTime.Now - cacheEntry.CacheTime).TotalHours < 24)
            {
                return cacheEntry.Data;
            }
        }


        switch (field.TableName)
        {
            case var name when string.Equals(name, "libelle_tel", StringComparison.InvariantCultureIgnoreCase):
                var libelleTel = _libelleTelRepository.GetAll(structureId);
                result = libelleTel.Select(x => new SelectLookup()
                {
                    Libelle = x.GetType().GetProperty(field.TableFieldName, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString(),
                    Value = x.GetType().GetProperty(field.TableFieldValue, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString(),
                }).OrderBy(x => x.Libelle).ToList();

                //Valeur du select à sélectionner
                var valueToSelectedLibelleTel = result.FirstOrDefault(v => v.Value == field.FieldValue.TrimStart('0'));
                if (valueToSelectedLibelleTel is not null)
                {
                    valueToSelectedLibelleTel.IsSelected = true;
                    field.FieldValue = valueToSelectedLibelleTel.Value;
                }
                break;


            case var name when string.Equals(name, "langue", StringComparison.InvariantCultureIgnoreCase):
                var languages = _languageRepository.GetAll(structureId);
                result = languages.Select(x => new SelectLookup
                {
                    Libelle = x.GetType().GetProperty(field.TableFieldName, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString(),
                    Value = x.GetType().GetProperty(field.TableFieldValue, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString()
                }).OrderBy(x => x.Libelle).ToList();

                var valueToSelectedlanguages = result.FirstOrDefault(v => v.Value.ToUpper() == field.FieldValue.ToUpper());
                if (valueToSelectedlanguages is not null)
                {
                    valueToSelectedlanguages.IsSelected = true;
                    field.FieldValue = valueToSelectedlanguages.Value;
                }
                break;

            case var name when string.Equals(name, "devise", StringComparison.InvariantCultureIgnoreCase):
                var currencies = _deviseRepository.GetAll(structureId).Where(d => d.Masquer != "O");
                result = currencies.Select(x => new SelectLookup
                {
                    Libelle = x.GetType().GetProperty(field.TableFieldName, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString(),
                    Value = x.GetType().GetProperty(field.TableFieldValue, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString()
                }).OrderBy(x => x.Libelle).ToList();

                var valueToSelectedDevises = result.FirstOrDefault(v => v.Value == HttpUtility.HtmlDecode(field.FieldValue));
                if (valueToSelectedDevises is not null)
                {
                    valueToSelectedDevises.IsSelected = true;
                    field.FieldValue = valueToSelectedDevises.Value;
                }
                break;

            case var name when string.Equals(name, "operateur", StringComparison.InvariantCultureIgnoreCase):
                var operateurs = _operatorRepository.GetAll(structureId);
                result = operateurs.Select(x => new SelectLookup
                {
                    Libelle = x.GetType().GetProperty(field.TableFieldName, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString(),
                    Value = x.GetType().GetProperty(field.TableFieldValue, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString()
                }).OrderBy(x => x.Libelle).ToList();

                var valueToSelectedOperateur = result.FirstOrDefault(v => v.Value == field.FieldValue.TrimStart('0'));
                if (valueToSelectedOperateur is not null)
                {
                    valueToSelectedOperateur.IsSelected = true;
                    field.FieldValue = valueToSelectedOperateur.Value;
                }
                break;


            case var name when string.Equals(name, "filiere", StringComparison.InvariantCultureIgnoreCase):
                var filieres = _filiereRepository.GetAll(structureId);
                result = filieres.Select(x => new SelectLookup
                {
                    Libelle = x.GetType().GetProperty(field.TableFieldName, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString(),
                    Value = x.GetType().GetProperty(field.TableFieldValue, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString()
                }).OrderBy(x => x.Libelle).ToList();

                var valueToSelectedFiliere = result.FirstOrDefault(v => v.Value == field.FieldValue.TrimStart('0'));
                if (valueToSelectedFiliere is not null)
                {
                    valueToSelectedFiliere.IsSelected = true;
                    field.FieldValue = valueToSelectedFiliere.Value;
                }
                break;


            case var name when string.Equals(name, "mode_paiement", StringComparison.InvariantCultureIgnoreCase):
                var modePaiement = _paymentMethodRepository.GetAll(structureId);
                result = modePaiement.Select(x => new SelectLookup
                {
                    Libelle = $"{x.GetType().GetProperty(field.TableFieldName, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString()} ({x.GetType().GetProperty(field.TableFieldValue, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString()})",
                    Value = x.GetType().GetProperty(field.TableFieldValue, BindingFlags.Instance | BindingFlags.Public | BindingFlags.IgnoreCase)?.GetValue(x)?.ToString()
                }).OrderBy(x => x.Libelle).ToList();

                var valueToSelectedModePaiement = result.FirstOrDefault(v => v.Value == field.FieldValue.TrimStart('0'));
                if (valueToSelectedModePaiement is not null)
                {
                    valueToSelectedModePaiement.IsSelected = true;
                    field.FieldValue = valueToSelectedModePaiement.Value;
                }
                break;


            default:
                throw new NotImplementedException();
        }

        _cache[cacheKey] = (DateTime.Now, result);

        return result;
    }

    public List<SelectLookup> GetSectionGroupe(List<ConfigIniSectionDTO> configIniSections, string sectionGroupeName)
    {
        if (configIniSections == null || !configIniSections.Any())
            return new List<SelectLookup>();

        return configIniSections
             .Where(s => s.SectionGroupe != null && s.SectionGroupe.Equals(sectionGroupeName, StringComparison.CurrentCultureIgnoreCase))
             .Select(s => new SelectLookup
             {
                 Libelle = s.SectionName,
                 Value = s.SectionName
             }).ToList();

    }


    #region Enregistrement du cofnig.ini

    private const string FOLDER_BACKUP = "__ConfigIniBackup";


    public async Task SauvegarderConfigIniAsync(int structureId, TstConfigIniViewModel tstConfigIniViewModel)
    {
        if (tstConfigIniViewModel == null || tstConfigIniViewModel.ConfigIniSectionsOfUser == null)
        {
            throw new ArgumentNullException(nameof(tstConfigIniViewModel));
        }

        try
        {
            string customerFilePath = GetCustomerPath(structureId);
            string userName = ExtractUserNameFromConfigIniUser(customerFilePath);
            tstConfigIniViewModel.UserNameWithTempFile = userName;

            RodrigueLogger.Debug(structureId, $"Début de SauvegarderConfigIniAsync pour l'utilisateur {tstConfigIniViewModel.UserNameWithTempFile}");

            var deviseCodeKey = tstConfigIniViewModel.ConfigIniSectionsOfUser.FirstOrDefault(s => s.SectionName.ToUpper().Contains("PARAM"))?.SectionFields.FirstOrDefault(k => k.FieldName.ToUpper().Contains("DEVISE_CODE"));

            if (deviseCodeKey != null)
                deviseCodeKey.FieldValue = deviseCodeKey.FieldValue.HtmlEncodeCustom();

            IncludeExterneKeys(ref tstConfigIniViewModel);

            // Supprimer les doublons des sections  
            tstConfigIniViewModel.ConfigIniSectionsOfUser = tstConfigIniViewModel.ConfigIniSectionsOfUser
                .GroupBy(s => s.SectionName)
                .Select(g => g.First())
                .ToList();

            // Convertir le modèle en XML  
            var xmlClientFinal = ConvertModelToXml(tstConfigIniViewModel.ConfigIniSectionsOfUser);

            string backUpPath = Path.Combine(Path.GetDirectoryName(customerFilePath), FOLDER_BACKUP);
            if (!Directory.Exists(backUpPath))
            {
                Directory.CreateDirectory(backUpPath);
            }
            else
            {
                int nbFiles = Directory.GetFiles(backUpPath).Count();

                if (nbFiles > 0 && nbFiles >= 10)
                {
                    _rodrigueNLogger.Trace(0, $"Création de la première sauvegarde du configIni.");
                }
                else if (nbFiles == 11)
                {
                    // Supprimer le plus vieux fichier et copier l'ancien  
                }
                if (File.Exists(customerFilePath))
                    File.Copy(customerFilePath, Path.Combine(backUpPath, $"{Path.GetFileName(customerFilePath)}_{DateTime.Now.Ticks}"));
            }

            // Supprimer le fichier temporaire  
            DeleteConfigIniTempFile(structureId, tstConfigIniViewModel.UserNameWithTempFile);

            // Sauvegarder le fichier XML final  
            await File.WriteAllTextAsync(customerFilePath, xmlClientFinal.ToString());

            RodrigueLogger.Debug(structureId, $"SauvegarderConfigIniAsync terminé pour l'utilisateur {tstConfigIniViewModel.UserNameWithTempFile}");
        }
        catch (Exception ex)
        {
            RodrigueLogger.Error(structureId, $"Une erreur est survenue lors de la sauvegarde du fichier config INI pour l'utilisateur {tstConfigIniViewModel.UserNameWithTempFile} - {ex.Message}");
            throw;
        }
    }




    #endregion



    #region Gestion du fichier temporaire de l'utilisateur


    private bool CreateTempFile(int structureId, string userName)
    {
        _rodrigueNLogger.Trace(structureId, $"Créer un fichier temporaire du config.ini de l'utilisateur {userName}");
        string customerFilePath = GetCustomerPath(structureId);
        if (File.Exists(customerFilePath))
        {
            try
            {
                CreateConfigIniTempFile(customerFilePath, userName);
            }
            catch (Exception ex)
            {
                _rodrigueNLogger.Error(structureId, $"CreateTempFileAsync utilisateur {userName} message {ex.Message}");
                throw;
            }
        }

        return true;
    }
    private bool CreateConfigIniTempFile(string configIniFilePath, string userName)
    {
        try
        {
            string destFileName = $"{Path.GetDirectoryName(configIniFilePath)}\\~{userName}_{Path.GetFileName(configIniFilePath)}";
            if (File.Exists(configIniFilePath))
            {
                var configFileInfo = new FileInfo(destFileName);
                File.Copy(configIniFilePath, configFileInfo.FullName, false);
                File.SetCreationTime(destFileName, DateTime.Now);
            }
            else
            {
                File.Create(configIniFilePath);
                File.Create(destFileName);
            }
        }
        catch (Exception e)
        {
            new Exception("Impossible de créer le fichier temporaire " + e.Message);
            throw;
        }
        return true;
    }
    private async Task DeleteFileExistsInEnvironmentAsync(string userName)
    {

        string directoryPath = _customerFilesPath.Replace("{environment}", _configuration["ENVIRONMENT"]!);

        try
        {
            // Rechercher uniquement les répertoires CONFIGSERVER dans le chemin principal
            var configServerDirs = Directory.EnumerateDirectories(directoryPath, "*", SearchOption.TopDirectoryOnly)
                .SelectMany(subDir => Directory.EnumerateDirectories(subDir, "CONFIGSERVER", SearchOption.TopDirectoryOnly))
                .Where(dir =>
                {
                    var parentDir = Path.GetFileName(Path.GetDirectoryName(dir));
                    return !string.IsNullOrEmpty(parentDir) && parentDir.Length == 4 && int.TryParse(parentDir, out _);
                });

            // Créer une tâche pour chaque répertoire CONFIGSERVER validé
            var tasks = configServerDirs.Select(async configServerDir =>
            {
                // Rechercher les fichiers correspondant au modèle dans ce répertoire
                var files = Directory.EnumerateFiles(configServerDir, $"~{userName}*", SearchOption.TopDirectoryOnly);

                // Supprimer chaque fichier trouvé
                foreach (var file in files)
                {
                    await Task.Run(() => File.Delete(file));
                }
            });

            // Attendre que toutes les tâches soient terminées
            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur : {ex.Message}");
        }
    }


    public bool DeleteConfigIniTempFile(int structureId, string userName)
    {
        string customerFilePath = GetCustomerPath(structureId);
        customerFilePath = GetTempFileUser(customerFilePath, userName);
        _rodrigueNLogger.Trace(structureId, $"Suppression du fichier temporaire {customerFilePath}");
        try
        {
            return DeleteTempFile(customerFilePath);
        }
        catch (Exception ex)
        {
            _rodrigueNLogger.Error(structureId, $"DeleteConfigIniTempFileAsync utilisateur {userName}  {ex.Message}");
            throw;
        }
    }
    private bool DeleteTempFile(string pathFileName)
    {
        if (File.Exists(pathFileName))
        {
            File.Delete(pathFileName);
            return true;
        }
        return false;
    }
    private string GetTempFileUser(string customerFilePath, string userName)
    {
        string newConfigIniFileNameWithUser = $"~{userName}_{Path.GetFileName(customerFilePath)}";
        return Path.Combine(Path.GetDirectoryName(customerFilePath), newConfigIniFileNameWithUser);

    }

    public bool HasTempFile(int structureId, string userName)
    {
        string customerFilePath = GetCustomerPath(structureId);
        bool hasTempfile = CustomerHasTempFile(customerFilePath, userName);

        RodrigueLogger.Debug(structureId, ($"{structureId} Utilisateur {userName} à un fichier temporaire {hasTempfile}"));
        return hasTempfile;
    }


    private bool CustomerHasTempFile(string pathFileName, string userName)
    {
        try
        {
            string tempFileName = $"{Path.GetDirectoryName(pathFileName)}\\~{userName}_{Path.GetFileName(pathFileName)}";
            if (File.Exists(tempFileName))
            {
                //if (IsFileOlder(tempFileName, thresholdAge))
                //{
                //    DeleteConfigIniTempFile(tempFileName);
                //    return false;
                //}
                return true;
            }
        }
        catch (Exception)
        {
            throw;
        }
        return false;
    }
    private bool IsTempFileOld(int structureId, string userName, TimeSpan thresholdAge)
    {
        string customerFilePath = GetCustomerPath(structureId);
        string tempFilePath = GetTempFileUser(customerFilePath, userName);
        bool isOldFile = IsUserTempFileOlder(tempFilePath, thresholdAge);
        if (isOldFile)
        {
            RodrigueLogger.Debug(structureId, $"{structureId} Utilisateur {userName} à un vieux fichier temporaire");
        }
        return isOldFile;
    }

    private bool IsUserTempFileOlder(string tempFileName, TimeSpan thresholdAge)
    {
        if (File.Exists(tempFileName))
        {
            DateTime tempFileCreationTime = File.GetCreationTime(tempFileName);
            DateTime loadingConfigIniTime = DateTime.Now;
            if ((loadingConfigIniTime - tempFileCreationTime) > thresholdAge)
            {
                return true;
            }
        }
        return false;
    }

    #endregion


    private List<ConfigIniSectionDTO> GetCustomerFusion(int structureId)
    {
        string defaultConfigIniPath = GetDefaultPath(structureId);
        string customerConfigIniPath = GetCustomerPath(structureId);

        var fusion = GetConfigIniMerged(defaultConfigIniPath, customerConfigIniPath);
        var configIniModel = ConvertXmlToModel(fusion);

        return _mapper.Map<List<ConfigIniSectionDTO>>(configIniModel);

    }

    #region Gestion de la lecture, fusion, conversion en DTO du fichier config.ini
    private IEnumerable<XElement>? GetConfigIniMerged(string configIniDefaultFilePath, string configIniCustomerFilePath)
    {
        //Si le fichier default n'existe pas 
        if (!File.Exists(configIniDefaultFilePath))
            throw new FileNotFoundException();

        var defaultConfigIni = XDocument.Load(configIniDefaultFilePath);
        if (File.Exists(configIniCustomerFilePath))
        {
            var customerConfigIni = XDocument.Load(configIniCustomerFilePath);
            XDocument xmlDoc = new XDocument(new XDeclaration("1.0", "utf-8", "yes"));
            XElement root = new XElement("configIni");

            //Récupération des sectionName du DEFAULT
            foreach (var item in defaultConfigIni.Descendants("Section").Attributes("Name"))
            {
                var sectionName = item.Value;

                XElement newSection = null;
                XAttribute newAttribute = null;
                newSection = new XElement("Section");

                //Récupération des elements de la sectionName du parent et de l'enfant 
                var sectionElements = GetSectionElements(sectionName, defaultConfigIni);
                var customerElements = GetSectionElements(sectionName, customerConfigIni);
                var sectionEtat = GetSectionEtat(sectionElements);

                var sectionGroupe = sectionElements.Attributes().FirstOrDefault(at => at.Name == "SectionGroupe")?.Value;
                
                newAttribute = new XAttribute("Name", sectionName);
                newSection.Add(newAttribute);
                newAttribute = new XAttribute("Etat", sectionEtat);
                newSection.Add(newAttribute);
                newAttribute = new XAttribute("SectionGroupe", sectionGroupe ?? string.Empty);
                newSection.Add(newAttribute);
                root.Add(newSection);

                //Récupération des attributs parents
                foreach (var parent in sectionElements.Elements())
                {
                    var defaultAttributs = parent.Attributes();
                    if (defaultAttributs != null)
                    {
                        var child = customerElements.Elements().Where(a => a.Name.LocalName.ToUpper() == parent.Name.LocalName.ToUpper()).FirstOrDefault();
                        if (child is not null)
                        {
                            child.RemoveAttributes();
                            XElement element = new XElement(child.Name);
                            element.Value = child.Value;

                            foreach (var childAttribute in defaultAttributs)
                            {
                                XAttribute attribute = new XAttribute(childAttribute.Name, childAttribute.Value);
                                element.Add(attribute);
                            }

                            element.Add(new XAttribute("isInCustomerXml", "true"));

                            if (root.Elements().FirstOrDefault(s => s.Attribute("Name").Value.Equals(sectionName)) is null)
                            {
                                newSection.Add(element);
                                root.Add(newSection);
                            }
                            else
                            {
                                var section = root.Elements().FirstOrDefault(s => s.Attribute("Name").Value.Equals(sectionName));
                                section.Add(element);
                            }
                        }
                        else
                        {
                            XElement dElement = new XElement(parent);
                            newSection.Add(dElement);
                        }
                    }
                }
                //}
            }
            xmlDoc.Add(root);
            return xmlDoc.Descendants("Section");
        }

        return defaultConfigIni.Descendants("Section");
    }

    private List<ConfigIniModel> ConvertXmlToModel(IEnumerable<XElement> fusion)
    {
        string environment = _configuration["ENVIRONMENT"] ?? "DEV";
        List<ConfigIniModel> configIniModel = new List<ConfigIniModel>();

        foreach (var item in fusion)
        {
            ConfigIniModel m = new();
            m.SectionName = item.Attribute("Name").Value;
            m.SectionEtat = item.Attribute("Etat").Value;
            m.SectionGroupe = item.Attribute("SectionGroupe")?.Value;
            m.Children = new();

            foreach (var child in item.Elements())
            {
                ChildModel modelChild = new ChildModel();
                modelChild.ChildKeyName = child.Name.ToString();
                modelChild.ChildKeyValue = child.Value;


                var defaultValueAttribute = child.Attribute("valeurParDefaut");

                if (string.IsNullOrWhiteSpace(modelChild.ChildKeyValue) && defaultValueAttribute != null)
                {
                    string defaultValue = defaultValueAttribute.Value;

                    if (defaultValue.StartsWith("{") && defaultValue.EndsWith("}"))
                    {
                        try
                        {
                            var configs = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(defaultValue);

                            if (configs != null && configs.Count > 0)
                            {
                                string key = (environment.Equals("DEV", StringComparison.OrdinalIgnoreCase) ||
                                             environment.Equals("TEST", StringComparison.OrdinalIgnoreCase))
                                             ? "TEST" : environment;

                                modelChild.ChildKeyValue = configs.ContainsKey(key) ? configs[key] : configs.Values.First();
                            }
                        }
                        catch
                        {
                            modelChild.ChildKeyValue = defaultValue;
                        }
                    }
                    else
                    {
                        modelChild.ChildKeyValue = defaultValue;
                    }
                }

                m.Children.Add(modelChild);

                foreach (var attribute in child.Attributes())
                {
                    modelChild.Attributes.Add(new AttributeModel(attribute.Name.ToString(), attribute.Value));
                }
            }

            m.ChildrenHasValue = ExistChildrenValue(m);
            configIniModel.Add(m);
        }

        return configIniModel;
    }



    private bool ExistChildrenValue(ConfigIniModel modelChild)
    {
        var childValue = modelChild.Children.Where(child => !string.IsNullOrWhiteSpace(child.ChildKeyValue) && child.Attributes.Any(k => k.AttributeName == "isInCustomerXml"));
        if (childValue.Any())
        {
            return true;
        }
        modelChild.ChildrenHasValue = false;
        return false;
    }


    #endregion



    private string GetDefaultPath(int structureId)
    {
        return _tstConfigIniPath.Replace("{structureId}", "DEFAULT").Replace("[structureId]", "DEFAULT");
    }


    private string GetCustomerPath(int structureId)
    {
        return _tstConfigIniPath.Replace("{structureId}", structureId.ToString("0000")).Replace("[structureId]", structureId.ToString("0000")); ;
    }

    private string ExtractUserNameFromConfigIniUser(string customerFilePath)
    {
        if (File.Exists(customerFilePath))
        {
            if (string.IsNullOrEmpty(customerFilePath))
            {
                return string.Empty;
            }

            //Cherche tous les fichiers commençant par ~
            string[] files = Directory.GetFiles(Path.GetDirectoryName(customerFilePath), "~*.xml");
            foreach (string file in files)
            {
                //si fichier contient _ alors on split
                if (file.Contains('_'))
                {
                    string[] fileName = Path.GetFileName(file).Split("_");
                    return Regex.Replace(fileName[0], "~", String.Empty);
                }
            }
            return string.Empty;
        }
        return string.Empty;
    }


    #region SPECIFIQUES     

    private IEnumerable<XElement> GetSectionElements(string sectionName, XDocument xdoc, string attributeName = "Name")
    {
        var sectionElements = from elements in xdoc.Descendants("configIni").Elements("Section")
                              where (string)elements.Attribute(attributeName) == sectionName
                              select elements;
        return sectionElements;
    }

    private string GetSectionEtat(IEnumerable<XElement> sectionElements)
    {
        string etat;
        foreach (var sectionElement in sectionElements.Attributes())
        {
            if (sectionElement.Name == "Etat")
            {
                if (sectionElement.Value == "EXTERNE" || sectionElement.Value == "EXCEPTION")
                {
                    etat = sectionElement.Value;
                    return etat;
                }
            }
        }
        return "DEFAULT";
    }



    #endregion

    public XDocument ConvertModelToXml(IEnumerable<ConfigIniSectionDTO> model)
    {
        XDocument finalXml = new XDocument();
        XElement root = new XElement("configIni");

        foreach (var item in model)
        {
            XElement newSection = new XElement("Section");
            XAttribute newAttribut = new XAttribute("Name", item.SectionName);
            newSection.Add(newAttribut);
            XAttribute newEtatAttribut = new XAttribute("Etat", "");
            newSection.Add(newEtatAttribut);

            foreach (var child in item.SectionFields)
            {
                if (!string.IsNullOrWhiteSpace(child.FieldValue))
                {
                    XElement childNode = new XElement(child.FieldName, child.FieldValue);
                    newSection.Add(childNode);
                }
            }
            root.Add(newSection);
        }

        finalXml.Add(root);
        return finalXml;
    }


    public List<TstModuleDTO> GetModulesAsync()
    {
        var modules = _tstModuleRepository.GetModulesWithDependancies();
        return _mapper.Map<List<TstModuleDTO>>(modules);
    }

    public TstModuleEntity? GetTstModuleById(int moduleId)
    {
        return _tstModuleRepository.GetById(moduleId);
    }


    public List<TstRoleDTO?> GetRolesAsync()
    {
        var roles = _tstRoleRepository.GetAllAsync();
        if (roles == null)
        {
            return new List<TstRoleDTO?>();
        }
        return roles.Select(role => _mapper.Map<TstRoleDTO?>(role)).ToList();
    }

    public TstRoleEntity? GetRoleByIdAsync(int roleId)
    {
        return _tstRoleRepository.GetByIdAsync(roleId);
    }

    public TstActiveDirectoryGroupEntity? GetActiveDirectoryGroupById(int activeDirectoryGroupId)
    {
        return _tstActiveDirectoryGroupRepository.GetById(activeDirectoryGroupId);
    }


    public async Task<List<TstActiveDirectoryGroupDTO>> GetAdGroupsAsync()
    {
        var entities = await _tstActiveDirectoryGroupRepository.GetActiveDirectoryGroupsRepositoryWithDependanciesAsync();
        return _mapper.Map<List<TstActiveDirectoryGroupDTO>>(entities);
    }


    public async Task AssignRoleToAdGroupAsync(int[] selectedAdGroupIds, int[] selectedModuleIds, int[] selectedRoleIds)
    {

        foreach (var adGroupId in selectedAdGroupIds)
        {
            foreach (var moduleId in selectedModuleIds)
            {
                foreach (var roleId in selectedRoleIds)
                {
                    TstAccessEntity access = new()
                    {
                        ActiveDirectoryGroupId = adGroupId,
                        ModuleId = moduleId,
                        RoleId = roleId,
                        TstActiveDirectoryGroup = new(),
                        TstModule = new(),
                        TstRole = new()
                    };

                    await _tstAccessRepository.AddAsync(access);
                }
            }
        }


    }


    public async Task CreateRoleAsync(TstRoleDTO role)
    {
        var entity = _mapper.Map<TstRoleEntity>(role);
        await _tstRoleRepository.AddAsync(entity);
    }

    //Creation d'un module  
    public async Task CreateModuleAsync(TstModuleDTO module)
    {
        var entity = _mapper.Map<TstModuleEntity>(module);
        await _tstModuleRepository.AddAsync(entity);
    }



    public async Task UpdateModuleAsync(TstModuleDTO module)
    {
        var entity = _mapper.Map<TstModuleEntity>(module);
        var existingEntity = await _tstModuleRepository.GetByIdAsync(module.ModuleId);

        if (existingEntity == null)
        {
            throw new KeyNotFoundException($"Module with ID {module.ModuleId} not found.");
        }

        existingEntity.ModuleName = entity.ModuleName;
        existingEntity.ModuleComment = entity.ModuleComment;

        await _tstModuleRepository.UpdateAsync(existingEntity);
    }

    /** 
     * Suppression d'un module 
     * 
     */
    public async Task<bool> DeleteModuleByIdAsync(int moduleId)
    {
        var existingEntity = await _tstModuleRepository.GetByIdAsync(moduleId);

        if (existingEntity == null)
        {
            throw new KeyNotFoundException($"Module with ID {moduleId} not found.");
        }

        return await _tstModuleRepository.DeleteAsync(moduleId);
    }



    public async Task CreateDirectoryGroupAsync(TstActiveDirectoryGroupDTO directorygroupe)
    {
        var entity = _mapper.Map<TstActiveDirectoryGroupEntity>(directorygroupe);
        await _tstActiveDirectoryGroupRepository.AddAsync(entity);
    }



    public async Task<List<TstAccessModel>> GetAllAccessesAsync()
    {
        var result = await _tstAccessRepository
           .GetAllCustomEntities()
           .Include(t => t.TstActiveDirectoryGroup)
           .Include(m => m.TstModule)
           .Include(r => r.TstRole)
           .ToEntityListAsync();


        // Mappe les entités d'accès vers des modèles d'accès
        return _mapper.Map<List<TstAccessModel>>(result);
    }


    public async Task<int?> GetParamsDeflagAsync(int structureId)
    {
        try
        {

            return await _paramsDeflagRepository.GetValueAsync(structureId);
        }
        catch (Exception ex)
        {
            return 40;
        }
    }

    public async Task<bool> UpdateParamsDeflagAsync(int structureId, int delaiDeflag)
    {
        try
        {
            if (delaiDeflag < 1 || delaiDeflag > 1440)
            {
                throw new ArgumentOutOfRangeException(nameof(delaiDeflag), "Le délai doit être entre 1 et 1440 minutes");
            }

            // Enregistrer la valeur saisie directement
            bool result = await _paramsDeflagRepository.SaveValueAsync(structureId, delaiDeflag);

            if (result)
            {
                _rodrigueNLogger.Debug(0, $"Délai deflag mis à jour: {delaiDeflag} minutes");
            }

            return result;
        }
        catch (Exception ex)
        {
            _rodrigueNLogger.Error(0, $"Erreur lors de la mise à jour du délai deflag: {ex.Message}");
            throw;
        }

    }

    public async Task<(int tempsExpiration, int delaiDeflag)> ChargerConfigurationTempsPanierAsync(int structureId, string userName, int? deflagFromDb)
    {

        int tempsExpiration = 20;
        int delaiDeflag = 40;

        try
        {
            if (deflagFromDb.HasValue)
            {
                delaiDeflag = deflagFromDb.Value;
            }

            var configIniViewModel = await GetConfigIniAsync(structureId, userName);

            if (configIniViewModel.ConfigIniSectionsOfUser != null)
            {
                var minutesToGoPaymentKey = configIniViewModel.ConfigIniSectionsOfUser.SelectMany(s => s.SectionFields.Where(f => f.FieldName.Contains("MINUTESTOGOTOPAYEMENT")));

                if (minutesToGoPaymentKey is not null)
                    tempsExpiration = int.Parse(minutesToGoPaymentKey.FirstOrDefault().FieldValue);
            }

        }
        catch (Exception ex)
        {
            throw;
        }


        return (tempsExpiration, delaiDeflag);
    }



    public async Task<bool> EnregistrerConfigurationTempsPanierAsync(int structureId, string userName, int tempsExpiration, int delaiDeflag)
    {

        try
        {

            //updae en BDD la valeur de deflag
            bool deflagSuccess = await UpdateParamsDeflagAsync(structureId, delaiDeflag);

            if (!deflagSuccess)
            {
                return false;
            }

            var configIniViewModel = await GetConfigIniAsync(structureId, userName);


            var paramSection = configIniViewModel.ConfigIniSectionsOfUser?.FirstOrDefault(s =>
                s.SectionName.Equals("PARAM", StringComparison.OrdinalIgnoreCase));

            if (paramSection == null)
            {
                paramSection = new ConfigIniSectionDTO
                {
                    SectionName = "PARAM",
                    SectionEtat = "DEFAULT",
                    SectionFields = new List<ConfigIniSectionFieldDTO>()
                };
                configIniViewModel.ConfigIniSectionsOfUser.Add(paramSection);
            }

            // Chercher ou créer la clé MINUTESTOGOTOPAYEMENT
            var minutesField = paramSection.SectionFields.FirstOrDefault(f =>
                f.FieldName.Equals("MINUTESTOGOTOPAYEMENT", StringComparison.OrdinalIgnoreCase));

            if (minutesField == null)
            {
                // Créer la clé si elle n'existe pas
                _rodrigueNLogger.Debug(structureId, "Création d'une nouvelle clé MINUTESTOGOTOPAYEMENT");
                minutesField = new ConfigIniSectionFieldDTO
                {
                    FieldName = "MINUTESTOGOTOPAYEMENT",
                    FieldValue = tempsExpiration.ToString(),
                    IsMandatory = false
                };
                paramSection.SectionFields.Add(minutesField);
            }
            else
            {
                minutesField.FieldValue = tempsExpiration.ToString();
            }

            // Sauvegarder la configuration
            await SauvegarderConfigIniAsync(structureId, configIniViewModel);

            UpdateCacheFile(structureId);

            return true;
        }
        catch (Exception ex)
        {
            throw;
        }

    }


    private bool UpdateCacheFile(int structureId)
    {
        try
        {
            string customerFilePath = GetCustomerPath(structureId);
            string? directoryPath = Path.GetDirectoryName(customerFilePath);
            if (directoryPath == null)
            {
                throw new InvalidOperationException("Le chemin du répertoire est null.");
            }
            string updateCachePath = Path.Combine(directoryPath, "updatecache.txt");

            File.WriteAllText(updateCachePath, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

            return true;
        }
        catch (Exception ex)
        {
            return false;
        }
    }


    public async Task<IEnumerable<ProfilAcheteurDTO>> GetBuyerProfiles(int structureId)
    {
        try
        {
          
            var buyerProfiles = await _buyerProfilRepository.GetAllCustomEntities(structureId)
                                        .Include(p => p.CouponsPromos)
                                        .ToEntityListAsync();
            var result =  _mapper.Map<IEnumerable<ProfilAcheteurDTO>>(buyerProfiles);

            return result;
        }
        catch (Exception ex)
        {
            _rodrigueNLogger.Error(structureId, $"Erreur lors de la récupération des profils acheteurs: {ex.Message}");
            throw;
        }
    }


    public async Task<int> GenerateCouponsAsync(int structureId, int numberOfCoupons, string template, string prefixe, int profilAcheteurId, DateTime dateDebValidite, DateTime dateFinValidite)
    {
        try
        { 
            var generatedCoupons = await _couponsPromoRepository.GenerateCouponsByStoredProcAsync(
                structureId,
                numberOfCoupons,
                template,
                prefixe,
                profilAcheteurId,
                dateDebValidite,
                dateFinValidite
            );
 
            return generatedCoupons.Count;
        }
        catch (Exception ex)
        {
            _rodrigueNLogger.Error(structureId, $"Erreur lors de la génération des coupons : {ex.Message}");
            throw;
        }
    }


    private async Task<bool> IsStructureInFileAsync(string environment, string phase, string structureId, bool isInclusion)
    {
        string filePath = GetXmlFilePath(environment, phase, isInclusion);

        try
        {
            if (!File.Exists(filePath))
            {
                return false;
            }

            XDocument doc = await Task.Run(() => XDocument.Load(filePath));

            return doc.Descendants("id_")
                     .Any(e => e.Value.Trim() == structureId) ||
                   doc.Descendants("id")
                     .Any(e => e.Value.Trim() == structureId);
        }
        catch (Exception)
        {
            return false;
        }
    }


    private string GetXmlFilePath(string environment, string phase, bool isInclusion)
    {
        // Construire le chemin à partir de la configuration ou d'un chemin par défaut
        string basePath = _configuration["ServicesRattrapePath"] ?? Path.Combine("D:", "lamine", "servicesRattrapeP");

        // Nom du fichier en fonction du type (inclusion ou exclusion)
        string fileName = isInclusion ? "inclusionsStructures.xml" : "exclusionsStructures.xml";

        return Path.Combine(basePath, environment.ToLower(), phase, fileName);
    }


    public async Task<List<LieuPhysiqueDTO>> GetNonMaskedLieuxPhysiquesAsync(int structureId)
    {
        try
        {
            var lieuxPhysiques = await _lieuPhysiqueRepository.GetNonMaskedLieuxPhysiquesAsync(structureId);

            if (lieuxPhysiques == null)
                return new List<LieuPhysiqueDTO>();

            return _mapper.Map<List<LieuPhysiqueDTO>>(lieuxPhysiques);
        }
        catch (Exception ex)
        {
            _rodrigueNLogger.Error(structureId, $"Erreur lors de la récupération des lieux physiques non masqués: {ex.Message}");
            throw;
        }
    }

    public async Task<List<int>> GetLieuxPhysiquesWithImagesAsync(int structureId)
    {
        try
        {
            return await _lieuPhysiqueRepository.GetLieuxPhysiquesWithImagesAsync(structureId);
        }
        catch (Exception ex)
        {
            _rodrigueNLogger.Error(structureId, $"Erreur lors de la récupération des lieux physiques avec images: {ex.Message}");
            throw;
        }
    }

    public async Task<bool> TransferPhotoPointageAsync(int structureId, int sourceLieuPhysiqueId, int targetLieuPhysiqueId)
    {
        try
        {
            bool result = await _lieuPhysiqueRepository.TransferPhotoPointageAsync(
                structureId,
                sourceLieuPhysiqueId,
                targetLieuPhysiqueId);

            if (result)
            {
                _rodrigueNLogger.Info(structureId, $"Transfert réussi: Lieu source {sourceLieuPhysiqueId} vers lieu cible {targetLieuPhysiqueId}");
            }
            else
            {
                _rodrigueNLogger.Error(structureId, $"Transfert échoué: Aucune image trouvée pour le lieu source {sourceLieuPhysiqueId}");
            }

            return result;
        }
        catch (Exception ex)
        {
            _rodrigueNLogger.Error(structureId, $"Erreur lors du transfert de pointage photo: {ex.Message}");
            throw;
        }
    }


    //Creation d'une methode compare 

    public async Task<(IEnumerable<dynamic> SourceOnly, IEnumerable<dynamic> DestinationOnly)> CompareLieuxPhysiquesAsync(int structureId, int sourceLieuPhysiqueId, int destinationLieuPhysiqueId)
    {
        try
        {

            var result = await _lieuPhysiqueRepository.CompareLieuxPhysiquesAsync(structureId, sourceLieuPhysiqueId, destinationLieuPhysiqueId);

            return result;
        }
        catch (Exception ex)
        {
            RodrigueLogger.Error(structureId, $"Erreur lors de la comparaison des lieux physiques : {ex.Message}");
            throw;
        }
    }

    public async Task ExportCustomReferenceSalleImageXmlAsync(int structureId, string environment)
    {
        try
        {
            
            var results = await _lieuPhysiqueRepository.GenerateReferenceSalleImageXmlWithCustomStructureAsync(structureId);

            var firstLieuPhysiqueId = results.FirstOrDefault()?.Lieu_physique_id ?? 0;
            if (firstLieuPhysiqueId == 0)
            {
                throw new InvalidOperationException("Aucun lieu_physique_id trouvé dans les résultats.");
            }

            string lieuIdSuffix = firstLieuPhysiqueId.ToString().Substring(0, firstLieuPhysiqueId.ToString().Length - 4);

            string basePath = _configuration["ImagesSeatingPlansPath"];
            string directoryPath = basePath
                .Replace("{environment}", environment)
                .Replace("{structureId}", structureId.ToString("D4"));

            // Construire le nom du fichier avec le suffixe
            string outputFilePath = System.IO.Path.Combine(directoryPath, $"iindexToXY_perspect{lieuIdSuffix}.xml");

            // Création du document XML
            var xmlDoc = new XDocument(
                new XDeclaration("1.0", "UTF-8", null),
                new XElement("documentRoot",
                    new XElement("rows",
                        from record in results
                        select new XElement("row",
                            new XAttribute("Lieud_id", record.Lieud_id),
                            new XAttribute("Lieu_physique_id", record.Lieu_physique_id),
                            new XAttribute("iindex", record.iindex),
                            new XAttribute("web_posx", record.web_posx),
                            new XAttribute("web_posy", record.web_posy)
                        )
                    )
                )
            );

            // Sauvegarde du fichier XML
            xmlDoc.Save(outputFilePath);
        }
        catch (Exception ex)
        {
            throw;
        }
    }


    public async Task ExportCustomReferenceWebXmlAsync(int structureId, string environment)
    {
        try
        {
            var results = await _lieuPhysiqueRepository.GenerateReferenceWebXmlWithVignetteAsync(structureId);

            if (!results.Any())
            {
                throw new InvalidOperationException("Aucun résultat trouvé pour générer le fichier XML.");
            }

            int firstLieuId = results.First().Lieu_id;

            string basePath = _configuration["ImagesSeatingPlansPath"];
            string directoryPath = basePath
                .Replace("{environment}", environment)
                .Replace("{structureId}", structureId.ToString("D4"));

            string fileName = $"iindexToImageName{firstLieuId}.xml";
            string outputFilePath = Path.Combine(directoryPath, fileName);

            // Création du document XML  
            var xmlDoc = new XDocument(
                new XDeclaration("1.0", "UTF-8", null),
                new XElement("documentRoot",
                    new XElement("rows",
                        from record in results
                        select new XElement("row",
                            new XAttribute("iindex", record.iindex),
                            new XAttribute("web_picture", $"/files/{structureId:D4}/indiv/images/seatingplans/{record.web_picture}")
                        )
                    )
                )
            );

            // Sauvegarde du fichier XML  
            xmlDoc.Save(outputFilePath);
        }
        catch (Exception ex)
        {
            _rodrigueNLogger.Error(structureId, $"Erreur lors de l'exportation du fichier XML : {ex.Message}");
            throw;
        }
    }
    public async Task<bool> TransfereVignetteAsync(int structureId, int sourceLieuPhysiqueId, int targetLieuPhysiqueId)
    {
        try
        {
            return await _lieuPhysiqueRepository.TransfereVignetteAsync(structureId, sourceLieuPhysiqueId, targetLieuPhysiqueId);
        }
        catch (Exception ex)
        {
            _rodrigueNLogger.Error(structureId, $"Erreur lors du transfert des vignettes: {ex.Message}");
            throw;
        }
    }

}
