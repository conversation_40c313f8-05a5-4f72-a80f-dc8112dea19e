info: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
info: Microsoft.Hosting.Lifetime[0]
      Hosting environment: local
info: Microsoft.Hosting.Lifetime[0]
      Content root path: D:\WORK\TST_LD\ThemisSupportTools_LD\Intranet\ThemisSupportTools.Web\ThemisSupportTools.Web
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
Erreur dans le timer : A task was canceled.
fail: Microsoft.AspNetCore.Components.Server.Circuits.RemoteNavigationManager[4]
      Navigation failed when changing the location to choose-structure/config-ini
      Microsoft.JSInterop.JSDisconnectedException: JavaScript interop calls cannot be issued at this time. This is because the circuit has disconnected and is being disposed.
         at Microsoft.JSInterop.JSRuntime.InvokeAsync[TValue](Int64 targetInstanceId, String identifier, CancellationToken cancellationToken, Object[] args)
         at Microsoft.JSInterop.JSRuntime.InvokeAsync[TValue](Int64 targetIns