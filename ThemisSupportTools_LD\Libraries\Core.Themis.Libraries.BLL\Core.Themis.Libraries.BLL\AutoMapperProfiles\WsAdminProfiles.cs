﻿using AutoMapper;
using Core.Themis.Libraries.Data.Entities.CouponsPromo;
using Core.Themis.Libraries.Data.Entities.Open.BuyerProfil;
using Core.Themis.Libraries.Data.Entities.WsAdmin;
using Core.Themis.Libraries.Data.Entities.WsAdmin.Login;
using Core.Themis.Libraries.Data.Entities.WsAdmin.Partner;
using Core.Themis.Libraries.Data.Entities.WsAdmin.Structure;
using Core.Themis.Libraries.Data.Entities.WsAdmin.ThemisSupportTools;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.CouponsPromo;
using Core.Themis.Libraries.DTO.ProfiteurAcheteur;
using Core.Themis.Libraries.DTO.Structures;
using Core.Themis.Libraries.DTO.TST;
using Core.Themis.Libraries.DTO.WSAdmin;
using Core.Themis.Libraries.DTO.WSAdmin.ConnexionsDTO;
using System;
using System.Linq;

namespace Core.Themis.Libraries.BLL.AutoMapperProfiles
{
    public class WsAdminProfiles : Profile
    {
        public WsAdminProfiles()
        {

            CreateMap<PartenairesRolesEntity, PartnerRoleDTO>()
                .ForMember(dest => dest.PartnerRoleId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.PartnerRoleCode, opt => opt.MapFrom(src => src.Code)).ReverseMap();

            //CreateMap<StructureAcceptPartenaireEntity, StructureDTO>()
            //    .ForMember(dest => dest.StructureId, opt => opt.MapFrom(src => src.StructureId))

            //    .ForMember(dest => dest.StructureName, opt => opt.MapFrom(src => src.Structure!.Name)).ReverseMap();

            CreateMap<PartenairesEntity, PartnerDTO>()
          .ForMember(dest => dest.PartnerId, opt => opt.MapFrom(src => src.PartenaireId))
          .ForMember(dest => dest.PartnerName, opt => opt.MapFrom(src => src.PartenaireNom))
          .ForMember(dest => dest.Password, opt => opt.MapFrom(src => src.Password))
          .ForMember(dest => dest.SecretKey, opt => opt.MapFrom(src => src.SecretKey))
          .ForMember(dest => dest.DateSupp, opt => opt.MapFrom(src => src.DateSupp))
          .ForMember(dest => dest.LstRolesOfPartner, opt => opt.MapFrom(src => src.PartenairesRoles))
          .ForMember(dest => dest.LstStructuresLinked, opt => opt.MapFrom(src => src.StructureAcceptPartenaires.Select(sap => new WsAdminStructureDTO() { StructureId = sap.StructureId, DateOperation = sap.DateOperation })))
          .ReverseMap();


            #region structures 

            CreateMap<WsAdminStructuresEntity, WsAdminStructureDTO>()
                .ForMember(dest => dest.StructureId, opt => opt.MapFrom(src => src.StructureId))
                 .ForMember(dest => dest.IsDeleted, opt => opt.MapFrom(src => src.Supp == 'O'))
                 .ForMember(dest => dest.DateDeleted, opt => opt.MapFrom(src => src.DateSupp))
                 .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name)).ReverseMap();




            CreateMap<LogsPartenairesEntity, LogsPartenairesDTO>()
                 .ReverseMap();

            #endregion

            CreateMap<LogsPartenairesEntity, LogsPartenaireDTO>()
                .ForMember(dest => dest.PartnerId, opt => opt.MapFrom(src => src.PartenaireId))
                .ForMember(dest => dest.StructureId, opt => opt.MapFrom(src => src.StructureId))
                .ForMember(dest => dest.Message, opt => opt.MapFrom(src => src.RequestBody))
                .ForMember(dest => dest.ResponseLength, opt => opt.MapFrom(src => src.RequestbodyLength))
                .ForMember(dest => dest.Version, opt => opt.MapFrom(src => src.Version))

                .ReverseMap();

            CreateMap<WsAdminStructuresEntity, WsAdminStructureDTO>()
               .ForMember(dest => dest.StructureId, opt => opt.MapFrom(src => src.StructureId))
                .ForMember(dest => dest.IsDeleted, opt => opt.MapFrom(src => src.Supp == 'O'))
                .ForMember(dest => dest.DateDeleted, opt => opt.MapFrom(src => src.DateSupp))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name)).ReverseMap();

            CreateMap<ConnectionStructureDatabaseInfoEntity, ConnectionStructureDatabaseInfoDTO>()
                .ForMember(dest => dest.DbName, opt => opt.MapFrom(src => src.DatabaseName))
                .ReverseMap();

            CreateMap<ConnexionswebTracingOfStructureEntity, ConnexionswebTracingOfStructureDTO>()

                .ForMember(dest => dest.StartDate, opt => opt.MapFrom(src => src.StartDate))
                .ForMember(dest => dest.StopDate, opt => opt.MapFrom(src => src.StopDate))
                .ReverseMap();

            //uniformisation des DTO
            CreateMap<WsAdminStructuresEntity, UnifiedWebTracingDTO>()
                 .ForMember(dest => dest.StructureId, opt => opt.MapFrom(src => src.StructureId))
                 .ForMember(dest => dest.StructureName, opt => opt.MapFrom(src => src.Name))
                 .ForMember(dest => dest.IsDeleted, opt => opt.MapFrom(src => src.Supp == 'O'))
                 .ForMember(dest => dest.DateDeleted, opt => opt.MapFrom(src => src.DateSupp));

            CreateMap<ConnexionswebTracingOfStructureEntity, UnifiedWebTracingDTO>()
                .ForMember(dest => dest.StartDate, opt => opt.MapFrom(src => src.StartDate))
                .ForMember(dest => dest.StopDate, opt => opt.MapFrom(src => src.StopDate));

            CreateMap<ConnectionStructureDatabaseInfoEntity, UnifiedWebTracingDTO>()
                .ForMember(dest => dest.DbName, opt => opt.MapFrom(src => src.DatabaseName));

            CreateMap<TstLoginEntity, TstLoginDTO>()
                .ForMember(dest => dest.IdentityName, opt => opt.MapFrom(src => src.IdentityName));

            CreateMap<ConnexionsLoginEntity, ConnexionLoginDTO>();
            CreateMap<ConnexionsLogsEntity, ConnexionsLogsDTO>();
            CreateMap<ActionsConnexionsEntity, ActionsConnexionsDTO>();
            CreateMap<ActionsReferenceToLogEntity, ActionsReferenceToLogDTO>();
            CreateMap<ConnexionsLogsEntity, ConnexionsLogsDTO>();

            CreateMap<ProfilAcheteurEntity, ProfilAcheteurDTO>();
            CreateMap<ProfilAcheteurDTO, ProfilAcheteurEntity>();



            CreateMap<CouponsPromoDTO, CouponsPromoEntity>()
                .ForMember(dest => dest.DateOperation, opt => opt.MapFrom(src => src.DateOperation))
                .ForMember(dest => dest.DateDebValidite, opt => opt.MapFrom(src => src.DateDebValidite))
                .ForMember(dest => dest.DateFinValidite, opt => opt.MapFrom(src => src.DateFinValidite));


            CreateMap<CouponsPromoEntity, CouponsPromoDTO>()
                  .ForMember(dest => dest.CouponStatus, opt => opt.MapFrom(src => GetStatus(src)));


        }

        private CouponStatus GetStatus(CouponsPromoEntity src)
        {
            var now = DateTime.Now;

            if (src.CommandeId.HasValue && src.CommandeId.Value > 0)
            {
                return CouponStatus.Used;
            }
            else if (now > src.DateFinValidite)
            {
                return CouponStatus.Expired;
            }
            else if (now >= src.DateDebValidite && now <= src.DateFinValidite)
            {
                return CouponStatus.Active;
            }
            else
            {
                return CouponStatus.None;
            }
        }
    }

}
